import { useCallback, useEffect, useMemo, useRef, useState } from "react";

export interface ReasoningTimes {
  [reasoningIndex: number]: number; // reasoning index -> elapsed time in seconds
}

export interface UseReasoningTimerOptions {
  messageId?: string;
  existingTimes?: ReasoningTimes; // 从 metadata 中恢复的已有计时数据
  onTimesChange?: (messageId: string, times: ReasoningTimes) => void; // 计时数据变化回调
}

export interface UseReasoningTimerReturn {
  reasoningTimes: ReasoningTimes;
  getDisplayTime: (reasoningIndex: number, isCurrentlyStreaming: boolean) => number | undefined;
  onReasoningStreamingChange: (reasoningIndex: number, isStreaming: boolean) => void;
  getCurrentTimes: () => ReasoningTimes;
}

/**
 * Hook for managing reasoning timing across multiple reasoning parts
 */
export function useReasoningTimer(options: UseReasoningTimerOptions = {}): UseReasoningTimerReturn {
  const { messageId, existingTimes = {}, onTimesChange } = options;

  // 使用 useMemo 稳定 existingTimes 的引用
  const stableExistingTimes = useMemo(() => existingTimes, [JSON.stringify(existingTimes)]);

  // 存储每个 reasoning 的计时数据
  const [reasoningTimes, setReasoningTimes] = useState<ReasoningTimes>(stableExistingTimes);

  // 存储每个 reasoning 的开始时间和计时器
  const startTimesRef = useRef<{ [reasoningIndex: number]: number }>({});
  const timersRef = useRef<{ [reasoningIndex: number]: NodeJS.Timeout }>({});
  const currentTimesRef = useRef<{ [reasoningIndex: number]: number }>({});

  // 存储上一次的流式传输状态，用于检测状态变化
  const prevStreamingStateRef = useRef<{ [reasoningIndex: number]: boolean }>({});

  // 清理所有计时器
  const clearAllTimers = useCallback(() => {
    Object.values(timersRef.current).forEach(timer => {
      if (timer) clearInterval(timer);
    });
    timersRef.current = {};
  }, []);

  // 当 messageId 变化时，重置所有状态
  useEffect(() => {
    clearAllTimers();
    startTimesRef.current = {};
    currentTimesRef.current = {};
    prevStreamingStateRef.current = {}; // 重置状态记录
    setReasoningTimes(stableExistingTimes);
  }, [messageId, stableExistingTimes, clearAllTimers]);

  // 组件卸载时清理计时器
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  // 处理 reasoning 流式状态变化
  const onReasoningStreamingChange = useCallback((reasoningIndex: number, isStreaming: boolean) => {
    // 检测状态变化：从 false 到 true（开始流式传输）
    const prevState = prevStreamingStateRef.current[reasoningIndex] || false;
    const stateChanged = prevState !== isStreaming;



    if (stateChanged) {
      if (isStreaming) {
        // 从 false 变为 true：开始计时
        const startTime = Date.now();
        startTimesRef.current[reasoningIndex] = startTime;
        currentTimesRef.current[reasoningIndex] = 0;

        // 创建计时器，每100ms更新一次当前时间
        const timer = setInterval(() => {
          const elapsed = Math.floor((Date.now() - startTime) / 1000);
          currentTimesRef.current[reasoningIndex] = elapsed; // 保持原始计时
        }, 100);

        timersRef.current[reasoningIndex] = timer;
      } else {
        // 从 true 变为 false：结束计时
        const timer = timersRef.current[reasoningIndex];
        if (timer) {
          clearInterval(timer);
          delete timersRef.current[reasoningIndex];
        }

        const startTime = startTimesRef.current[reasoningIndex];
        if (startTime) {
          const elapsed = Math.floor((Date.now() - startTime) / 1000);
          const finalTime = elapsed === 0 ? 1 : elapsed; // 只有当计时为 0 时才设置为 1 秒

          setReasoningTimes(prev => {
            const newTimes = {
              ...prev,
              [reasoningIndex]: finalTime
            };

            // 通知计时数据变化
            if (messageId && onTimesChange) {
              onTimesChange(messageId, newTimes);
            }

            return newTimes;
          });

          currentTimesRef.current[reasoningIndex] = finalTime;
          delete startTimesRef.current[reasoningIndex];
        }
      }
    }

    // 更新状态记录
    prevStreamingStateRef.current[reasoningIndex] = isStreaming;


  }, [messageId, onTimesChange, reasoningTimes]);

  // 获取显示时间
  const getDisplayTime = useCallback((reasoningIndex: number, isCurrentlyStreaming: boolean): number | undefined => {
    if (isCurrentlyStreaming) {
      // 正在流式传输，返回当前计时，显示时最小值为 1 秒
      const currentTime = currentTimesRef.current[reasoningIndex] || 0;
      return Math.max(1, currentTime);
    } else {
      // 已完成，返回最终时间
      const finalTime = reasoningTimes[reasoningIndex];


      if (finalTime !== undefined) {
        return finalTime === 0 ? 1 : finalTime; // 只有当时间为 0 时才显示为 1 秒
      }

      // 如果还没有记录，返回 1 秒作为临时显示（很快会被估算时间替换）
      return 1;
    }
  }, [reasoningTimes]);

  // 获取当前所有计时数据（用于保存到 metadata）
  const getCurrentTimes = useCallback((): ReasoningTimes => {
    const result = { ...reasoningTimes };
    
    // 包含正在进行的计时
    Object.entries(currentTimesRef.current).forEach(([index, time]) => {
      const reasoningIndex = parseInt(index);
      if (time > 0) {
        result[reasoningIndex] = time;
      }
    });

    return result;
  }, [reasoningTimes]);

  return {
    reasoningTimes,
    getDisplayTime,
    onReasoningStreamingChange,
    getCurrentTimes,
  };
}
